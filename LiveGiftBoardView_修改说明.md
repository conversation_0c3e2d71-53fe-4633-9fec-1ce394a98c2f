# LiveGiftBoardView 修改说明

## 修改概述
参考 FMNoteRewardAmountView 的积分抵扣逻辑，对 LiveGiftBoardView 进行了以下修改：

## 主要修改内容

### 1. UI布局调整
- **礼物排列方式**：从纵向排列改为横向排列
- **CollectionView配置**：
  - 滚动方向：`UICollectionViewScrollDirectionHorizontal`
  - Cell尺寸：100x130（符合需求规格）
  - 间距：10像素
- **移除Cell内发送按钮**：统一使用底部的打赏按钮

### 2. 积分抵扣功能
- **积分数据管理**：
  - 添加 `userPoints` 属性存储用户积分
  - 添加 `pointsRatio` 属性设置兑换比例（1积分=1金币）
  
- **积分计算逻辑**：
  - `calculateDeductibleCoins`：计算可抵扣的金币数量
  - `getCurrentUsePoints`：获取当前使用的积分数量
  - `getActualPayAmount`：计算实际需要支付的金币数量

### 3. UI组件新增
- **积分显示区域**：
  - 积分标题标签
  - 积分状态描述（显示积分数量和抵扣情况）
  
- **支付信息区域**：
  - 金币支付标题
  - 需支付金币数显示
  - 金币余额显示
  - 积分抵扣提示

- **统一打赏按钮**：
  - 替代原有Cell内的发送按钮
  - 根据余额状态显示不同文案

### 4. 交互逻辑优化
- **默认选中**：打开弹窗时默认选中第一个礼物
- **切换更新**：切换礼物时同步更新积分可抵扣金币数及需支付金币数
- **按钮状态**：
  - 余额充足：显示"打赏老师"
  - 余额不足：显示"金币不足，请先充值"，点击跳转充值中心

### 5. 积分显示规则
- **有积分可抵扣**：显示"积分数 -可抵扣金币数"（红色高亮抵扣部分）
- **无积分可抵扣**：显示"积分数 暂无可用"
- **计算规则**：与笔记支付处保持一致

### 6. 高度调整
- 弹窗高度增加80像素以容纳新增的积分UI和按钮区域

## 新增属性

### LiveGiftBoardView.h
```objective-c
/// 当前选中的礼物模型
@property (nonatomic, strong) LiveGiftModel *selectedGiftModel;
```

### LiveGiftBoardView.m
```objective-c
// 积分相关属性
@property (nonatomic, assign) NSInteger userPoints;
@property (nonatomic, assign) NSInteger pointsRatio;

// UI组件
@property (nonatomic, strong) UILabel *pointsTitleLabel;
@property (nonatomic, strong) UILabel *pointsDescLabel;
@property (nonatomic, strong) UILabel *coinPayTitleLabel;
@property (nonatomic, strong) UILabel *coinPayAmountLabel;
@property (nonatomic, strong) UIButton *sendGiftBtn;
```

## 新增方法

### 积分计算方法
- `setupPointsData`：初始化积分数据
- `calculateDeductibleCoins`：计算可抵扣金币数量
- `getCurrentUsePoints`：获取当前使用积分数量
- `getActualPayAmount`：计算实际支付金币数量

### UI更新方法
- `setupPaymentInfoView:`：设置积分和支付信息UI
- `setupSendGiftButton:`：设置打赏按钮
- `updatePointsDisplay`：更新积分显示
- `updatePayAmountDisplay`：更新支付金额显示
- `updatePaymentDisplay`：更新支付相关显示
- `updateSendButtonState`：更新发送按钮状态

### 交互方法
- `sendGiftBtnClick`：打赏按钮点击事件

## 兼容性说明
- 保持原有的回调接口不变
- 保持原有的数据源设置方式
- 新增的积分抵扣功能向后兼容，不影响现有功能

## 测试建议
1. 测试横向滚动礼物选择
2. 测试积分抵扣计算准确性
3. 测试按钮状态切换
4. 测试充值跳转功能
5. 测试礼物发送成功后的状态更新