//
//  LiveGiftBoardView.h
//  QCYZT
//
//  Created by shumi on 2023/5/10.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import <UIKit/UIKit.h>
@class  LiveGiftMsgInfo;
@class  FMLiveDetailModel;
@class  LiveGiftModel;
NS_ASSUME_NONNULL_BEGIN


@interface LiveGiftBoardView : UIView
/// 送礼物回调
@property (nonatomic,copy) void(^sendGiftBlock)(LiveGiftModel *model);
/// 送礼合规失败
@property (nonatomic,copy) void(^judgeConfirmOrderFailur)();
/// 充值回调
@property (nonatomic,copy) void(^rechargeBlock)();
/// 直播间id
@property (nonatomic, strong) FMLiveDetailModel *detailModel;
/// 被选中的礼物id
@property (nonatomic,assign) NSInteger giftId;
/// 礼物数据源
@property (nonatomic, strong) NSArray *dataArr;
/// 当前选中的礼物模型
@property (nonatomic, strong) LiveGiftModel *selectedGiftModel;
- (void)show;
- (instancetype)initPageLandScape:(BOOL)isLandScape;
- (instancetype)init UNAVAILABLE_ATTRIBUTE;
- (instancetype)initWithFrame:(CGRect)frame UNAVAILABLE_ATTRIBUTE;
@end

NS_ASSUME_NONNULL_END
