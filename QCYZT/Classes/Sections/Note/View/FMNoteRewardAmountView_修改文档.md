# FMNoteRewardAmountView 积分抵扣功能修改文档

## 修改概述
为笔记打赏功能添加积分抵扣功能，积分自动优先抵扣，无需手动选择。

## 主要修改内容

### 1. 新增属性
```objective-c
// 积分相关属性
@property (nonatomic, assign) NSInteger userPoints;        // 用户积分数量
@property (nonatomic, assign) NSInteger pointsRatio;       // 积分兑换比例（1积分=1金币）

// 积分UI组件
@property (nonatomic, strong) UILabel *pointsTitleLabel;   // 积分标题
@property (nonatomic, strong) UILabel *pointsDescLabel;    // 积分状态描述
```

### 2. UI布局调整
- **积分行位置**：移至金币支付上方
- **积分标题**：使用 `UIColor.up_textSecondaryColor`
- **问号按钮**：添加积分说明按钮，使用 `pay_wenhao` 图标
- **积分显示**：右侧显示积分数量和抵扣状态
- **提示文字**：改为"若有积分，优先使用积分抵扣"（灰色，不可点击）

### 3. 积分显示逻辑
```objective-c
- (void)updatePointsDisplay {
    // 积分不够1金币
    if (self.userPoints < self.pointsRatio) {
        显示: "X 暂无可用" (使用 up_textSecondary2Color)
    }
    // 有积分可抵扣
    else if (deductibleCoins > 0) {
        显示: "X -Y积分" (其中"-Y积分"使用 up_riseColor 红色)
    }
    // 有积分但无法抵扣
    else {
        显示: "X" (使用 up_textPrimaryColor)
    }
}
```

### 4. 核心方法
- `setupPointsData()` - 初始化积分数据
- `setupPointsView()` - 创建积分UI（含问号按钮）
- `calculateDeductibleCoins()` - 计算可抵扣金币数量
- `getCurrentUsePoints()` - 获取当前使用积分数量（自动抵扣）
- `getActualPayAmount()` - 计算实际需支付金币数量
- `updatePointsDisplay()` - 更新积分显示
- `updatePayPriceDisplay()` - 更新支付金额显示
- `helpButtonTapped()` - 积分说明弹窗

### 5. 支付逻辑优化
- **自动抵扣**：有积分时自动优先使用，无需手动选择
- **金额计算**：支付金额 = 原金额 - 积分抵扣金额
- **按钮状态**：积分+金币余额足够时显示"打赏老师"
- **API调用**：传递正确的 `usePoints` 参数

### 6. 界面高度调整
- 原高度：356px → 新高度：396px（增加40px容纳积分UI）

### 7. 颜色适配
- 使用项目统一的颜色系统（up_textSecondaryColor、up_riseColor等）
- 支持暗黑模式适配

## 功能特点
1. **自动化**：积分自动优先抵扣，用户无需操作
2. **直观显示**：清晰显示积分数量和抵扣状态
3. **智能计算**：实时计算实际支付金额
4. **用户友好**：提供积分说明和获取途径
5. **兼容性**：与现有支付流程完全兼容

## 测试要点
- 无积分时显示"0 暂无可用"
- 有积分时显示"当前积分 -抵扣积分"格式
- 积分+金币余额判断逻辑正确
- 支付成功后积分和金币数据更新
- 问号按钮点击显示积分说明
