//
//  InviteRegisterViewController.m
//  QCYZT
//
//  Created by macPro on 2017/12/7.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "InviteRegisterViewController.h"
#import "FMMakeInvitationViewController.h"
#import "FMInviteRegisterTableViewHeader.h"
#import "FMInviteRegisterSectionHeader.h"
#import "FMInviteRegisterInviteRecordCell.h"
#import "InviteRegisterInviteRecordModel.h"
#import "FMInviteReigsterRulePopView.h"
#import "FMInviteRegisterNodataCell.h"

@interface InviteRegisterViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) FMInviteRegisterTableViewHeader *tableHeader;

@property (nonatomic, strong) NSDictionary *inviteInfo;
@property (nonatomic, strong) NSMutableArray *inviteRecordArr;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;

@end

@implementation InviteRegisterViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = @"拉新有礼";
    self.view.backgroundColor = UIColor.up_contentBgColor;

    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(@0);
        make.bottom.equalTo(@(0));
    }];
    
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 10;

    [self.tableView.mj_header beginRefreshing];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
        
    [self configNavWhiteColorWithCloseSEL:@selector(closeBtnClick)];
}

- (void)closeBtnClick {
    [self.navigationController popViewControllerAnimated:YES];
}


#pragma mark - UITableViewDelegate
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.inviteRecordArr.count) {
        return self.inviteRecordArr.count;
    }
    
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.inviteRecordArr.count) {
        FMInviteRegisterInviteRecordCell *cell = [tableView reuseCellClass:[FMInviteRegisterInviteRecordCell class]];
        cell.model = self.inviteRecordArr[indexPath.row];
        cell.isLast = (indexPath.row == self.inviteRecordArr.count - 1);
        return cell;
    }
    
    FMInviteRegisterNodataCell *cell = [tableView reuseCellClass:[FMInviteRegisterNodataCell class]];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.inviteRecordArr.count) {
        return UI_Relative_WidthValue(65);
    }
    
    return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMInviteRegisterNodataCell class]) configuration:^(id cell) {
    }];
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    FMInviteRegisterSectionHeader *header = [tableView reuseViewClass:[FMInviteRegisterSectionHeader class]];
    header.inviterNum = [self.inviteInfo[@"total"] integerValue];
    return header;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return [tableView fd_heightForHeaderFooterViewWithIdentifier:NSStringFromClass([FMInviteRegisterSectionHeader class]) configuration:nil];
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - Request
- (void)getData {
    WEAKSELF;
    [HttpRequestTool getInviteInfoListWithWithPage:self.page pageSize:self.pageSize start:^{
    } failure:^{
        [__weakSelf endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [__weakSelf endRefreshForSuccess];

            if (__weakSelf.page == 1) {
                [__weakSelf.inviteRecordArr removeAllObjects];
                [__weakSelf.tableView.mj_footer resetNoMoreData];
            }

            __weakSelf.inviteInfo = dic[@"data"];
            NSArray *inviteRecordArr = [NSArray modelArrayWithClass:[InviteRegisterInviteRecordModel class] json:dic[@"data"][@"inviteInfoList"]];
            if (inviteRecordArr.count < __weakSelf.pageSize) {
                [__weakSelf.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            __weakSelf.tableHeader.inviteInfo = __weakSelf.inviteInfo;

            [__weakSelf.inviteRecordArr addObjectsFromArray:inviteRecordArr];
            if (__weakSelf.inviteRecordArr.count == 0)  {
                //显示占位
                __weakSelf.tableView.mj_footer.hidden = YES;
            } else {
                __weakSelf.tableView.mj_footer.hidden = NO;
            }
            [__weakSelf.tableView reloadData];
            
            __weakSelf.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"活动规则" style:UIBarButtonItemStylePlain target:self action:@selector(clickedRuleItem)];
        } else {
            [__weakSelf endRefreshForFailure];
            
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)getRewardWithInviteCode:(NSString *)code {
    WEAKSELF;
    [HttpRequestTool getRewardWithInviteCode:code start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        
        if ([dic[@"status"] isEqualToString:@"1"]) {
            ShowConfirm(__weakSelf, nil, dic[@"errmessage"], @"取消", @"我要邀请", nil, ^{
                FMMakeInvitationViewController *vc = [[FMMakeInvitationViewController alloc] init];
                [__weakSelf.navigationController pushViewController:vc animated:YES];
            });
        } else {
            ShowAlert(__weakSelf, nil, dic[@"errmessage"], @"确定", ^{
                
            });
        }
    }];
}

#pragma mark - Private
- (void)headerAction {
    self.page = 1;
    [self getData];
}

- (void)footerAction {
    self.page++;
    [self getData];
}

- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}
//成功状态下停止refresh
- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

- (void)clickedRuleItem {
    FMInviteReigsterRulePopView *popView = [[FMInviteReigsterRulePopView alloc] init];
    popView.reminderString = self.inviteInfo[@"description"];
    [popView show];
}

#pragma mark - Getter/Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(headerAction) footerTarget:self footerAction:@selector(footerAction)];
        
        [_tableView registerViewClass:[FMInviteRegisterSectionHeader class]];
        [_tableView registerCellClass:[FMInviteRegisterInviteRecordCell class]];
        [_tableView registerCellClass:[FMInviteRegisterNodataCell class]];


        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        _tableView.tableHeaderView = self.tableHeader;
        
        UIView *footerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, CGFLOAT_MIN)];
        _tableView.tableFooterView = footerView;
    }
    
    return _tableView;
}

- (FMInviteRegisterTableViewHeader *)tableHeader {
    if (!_tableHeader) {
        _tableHeader = [[FMInviteRegisterTableViewHeader alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_Relative_WidthValue(580))];
        WEAKSELF
        _tableHeader.commitBlock = ^(NSString * _Nonnull code) {
            [__weakSelf getRewardWithInviteCode:code];
        };
    }
    
    return _tableHeader;
}

- (NSMutableArray *)inviteRecordArr {
    if (!_inviteRecordArr) {
        _inviteRecordArr = [NSMutableArray array];
    }
    
    return _inviteRecordArr;
}


@end
