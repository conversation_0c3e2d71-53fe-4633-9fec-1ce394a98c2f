//
//  FMInviteRegisterInviteRecordCell.m
//  QCYZT
//
//  Created by macPro on 2017/12/7.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMInviteRegisterInviteRecordCell.h"
#import "InviteRegisterInviteRecordModel.h"

@interface FMInviteRegisterInviteRecordCell()

@property (nonatomic, strong) UIView *whiteView;
@property (nonatomic, strong) UIImageView *headImgV;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *timeLabel;
@property (nonatomic, strong) UILabel *coinNumLabel;

@end

@implementation FMInviteRegisterInviteRecordCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self createViews];
    }
    return  self;
}

- (void)createViews{
    self.contentView.backgroundColor = ColorWithHex(0xffbc64);

    UIView *whiteView = [[UIView alloc] init];
    [self.contentView addSubview:whiteView];
    [whiteView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(@0);
        make.centerX.equalTo(@0);
        make.width.equalTo(@(UI_Relative_WidthValue(345)));
    }];
    whiteView.backgroundColor = UIColor.up_contentBgColor;
    self.whiteView = whiteView;
    
    [self.whiteView addSubview:self.headImgV];
    [self.headImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.left.equalTo(@(UI_Relative_WidthValue(15)));
        make.width.height.equalTo(@(UI_Relative_WidthValue(40)));
    }];
    UI_View_Radius(self.headImgV, UI_Relative_WidthValue(20));

    [self.whiteView addSubview:self.coinNumLabel];
    [self.coinNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.right.equalTo(@(UI_Relative_WidthValue(-15)));
    }];

    [self.whiteView addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.headImgV.mas_right).offset(15);
        make.top.equalTo(@(UI_Relative_WidthValue(14)));
        make.right.lessThanOrEqualTo(self.coinNumLabel.mas_left).offset(-15);
    }];

    [self.whiteView addSubview:self.timeLabel];
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.top.equalTo(self.nameLabel.mas_bottom).offset(UI_Relative_WidthValue(5));
        make.right.lessThanOrEqualTo(self.coinNumLabel.mas_left).offset(-15);
    }];

    [self.whiteView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.bottom.equalTo(@0);
        make.height.equalTo(@0.7);
    }].backgroundColor = UIColor.fm_sepline_color;
}

- (void)setModel:(InviteRegisterInviteRecordModel *)model {
    _model = model;
    
    [self.headImgV sd_setImageWithURL:[NSURL URLWithString:model.invited_ico] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.nameLabel.text = model.invited_name;
    
    NSDate *nowDate = [NSDate dateWithTimeIntervalSince1970:model.create_time/1000];
    NSString *timeStr = [NSString stringFromDate:nowDate format:@"yyyy/MM/dd"];
    self.timeLabel.text = [NSString stringWithFormat:@"邀请时间：%@", timeStr];
    
    self.coinNumLabel.text = model.reward_description;
}

- (void)setIsLast:(BOOL)isLast {
    _isLast = isLast;
    
    if (isLast) {
        [self.whiteView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_Relative_WidthValue(345), UI_Relative_WidthValue(65)) cornerRadii:CGSizeMake(UI_Relative_WidthValue(10), UI_Relative_WidthValue(10)) byRoundingCorners:UIRectCornerBottomLeft | UIRectCornerBottomRight];
    } else {
        [self.whiteView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_Relative_WidthValue(345), UI_Relative_WidthValue(65)) cornerRadii:CGSizeMake(0, 0) byRoundingCorners:UIRectCornerBottomLeft | UIRectCornerBottomRight];
    }
}

- (UIImageView *)headImgV {
    if (!_headImgV) {
        _headImgV = [UIImageView new];
        _headImgV.contentMode = UIViewContentModeScaleAspectFill;
    }
    
    return _headImgV;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(UI_Relative_WidthValue(14)) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        [_nameLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    }
    
    return _nameLabel;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(UI_Relative_WidthValue(13)) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _timeLabel;
}

- (UILabel *)coinNumLabel {
    if (!_coinNumLabel) {
        _coinNumLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(UI_Relative_WidthValue(15)) textColor:FMNavColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    }
    
    return _coinNumLabel;
}

@end
